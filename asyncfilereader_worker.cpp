
#include "asyncfilereader_worker.h"
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QDateTime>
#include <QDebug>
#include <QTextCodec>
#include <QRegularExpression>

FileReadWorker::FileReadWorker(QObject* parent)
    : QObject(parent), m_paused(false), m_cancelled(false)
{
    qRegisterMetaType<LogEntry>("LogEntry");
    qRegisterMetaType<QVector<LogEntry>>("QVector<LogEntry>");
}

void FileReadWorker::doRead(const QString& filePath, const QString& encoding, int chunkSize, int maxEntries) {
    QFileInfo fi(filePath);
    if (!fi.exists() || !fi.isReadable()) {
        emit error(QString("文件不可读: %1").arg(filePath));
        return;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emit error(QString("无法打开文件: %1").arg(file.errorString()));
        return;
    }

    QTextCodec* codec = detectFileEncoding(filePath);
    if (!codec) codec = QTextCodec::codecForName(encoding.toUtf8());
    if (!codec) codec = QTextCodec::codecForName("UTF-8");

    QTextStream stream(&file);
    stream.setCodec(codec);

    QVector<LogEntry> chunk;
    chunk.reserve(chunkSize);

    int lineNumber = 0;
    int totalEntries = 0;
    qint64 startTime = QDateTime::currentMSecsSinceEpoch();

    QString line;
    while (!stream.atEnd()) {
        {
            QMutexLocker locker(&m_mutex);
            while (m_paused) m_pauseCond.wait(&m_mutex);
            if (m_cancelled) break;
        }

        line = stream.readLine();
        lineNumber++;
        if (line.trimmed().isEmpty()) continue;

        chunk.append(parseLogLine(line, lineNumber));
        totalEntries++;

        if (chunk.size() >= chunkSize || (maxEntries > 0 && totalEntries >= maxEntries)) {
            emit chunkReady(chunk, false);
            emit progress(totalEntries, 0, 0); // 发送进度更新
            chunk.clear();
            chunk.reserve(chunkSize);
            if (maxEntries > 0 && totalEntries >= maxEntries) break;
        }
    }

    if (!chunk.isEmpty()) {
        emit chunkReady(chunk, true);
    }

    file.close();
    emit finished(totalEntries, QDateTime::currentMSecsSinceEpoch() - startTime);
}

void FileReadWorker::pause() {
    QMutexLocker locker(&m_mutex);
    m_paused = true;
}

void FileReadWorker::resume() {
    QMutexLocker locker(&m_mutex);
    m_paused = false;
    m_pauseCond.wakeAll();
}

void FileReadWorker::cancel() {
    QMutexLocker locker(&m_mutex);
    m_cancelled = true;
    m_pauseCond.wakeAll();
}

QTextCodec* FileReadWorker::detectFileEncoding(const QString& filePath) const {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) return nullptr;
    QByteArray data = file.read(8192);
    file.close();
    if (data.startsWith("\xEF\xBB\xBF")) return QTextCodec::codecForName("UTF-8");
    if (data.startsWith("\xFF\xFE") || data.startsWith("\xFE\xFF")) return QTextCodec::codecForName("UTF-16");
    QTextCodec* utf8Codec = QTextCodec::codecForName("UTF-8");
    QTextCodec::ConverterState state;
    utf8Codec->toUnicode(data.constData(), data.size(), &state);
    if (state.invalidChars == 0) return utf8Codec;
    return QTextCodec::codecForName("GBK");
}

LogEntry FileReadWorker::parseLogLine(const QString& line, int lineNumber) const {
    // 尝试解析时间戳
        QDateTime timestamp = parseTimestamp(line);

        // 检测日志级别
        LogEntry::LogLevel level = detectLogLevel(line);

        // 构建源信息
        QString source = QString("File:%1").arg(lineNumber);

        // 消息内容（去除时间戳和级别信息后的内容）
        QString message = line.trimmed();

        return LogEntry(timestamp, level, source, message, "");
}

LogEntry::LogLevel FileReadWorker::detectLogLevel(const QString& text) const
{
    QString upperText = text.toUpper();

    if (upperText.contains("ERROR") || upperText.contains("错误")) {
        return LogEntry::LogLevel::Error;
    } else if (upperText.contains("WARN") || upperText.contains("警告")) {
        return LogEntry::LogLevel::Warning;
    } else if (upperText.contains("INFO") || upperText.contains("信息")) {
        return LogEntry::LogLevel::Info;
    } else if (upperText.contains("DEBUG") || upperText.contains("调试")) {
        return LogEntry::LogLevel::Debug;
    }
    return LogEntry::LogLevel::Info; // 默认级别
}

QDateTime FileReadWorker::parseTimestamp(const QString& text) const
{
    // 常见的时间戳格式
    QStringList patterns = {
        "yyyy-MM-dd hh:mm:ss.zzz",
        "yyyy-MM-dd hh:mm:ss",
        "yyyy/MM/dd hh:mm:ss",
        "MM-dd hh:mm:ss",
        "hh:mm:ss.zzz",
        "hh:mm:ss"
    };

    // 尝试匹配各种时间戳格式
    QRegularExpression timeRegex(R"(\d{1,4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}(?:\.\d{1,3})?|\d{1,2}:\d{1,2}:\d{1,2}(?:\.\d{1,3})?)");
    QRegularExpressionMatch match = timeRegex.match(text);

    if (match.hasMatch()) {
        QString timeStr = match.captured(0);

        for (const QString& pattern : patterns) {
            QDateTime dateTime = QDateTime::fromString(timeStr, pattern);
            if (dateTime.isValid()) {
                // 如果只有时间没有日期，使用今天的日期
                if (!timeStr.contains('-') && !timeStr.contains('/')) {
                    QDate today = QDate::currentDate();
                    dateTime.setDate(today);
                }
                return dateTime;
            }
        }
    }

    // 如果无法解析时间戳，返回当前时间
    return QDateTime::currentDateTime();
}

AsyncFileReader::AsyncFileReader(QObject* parent)
    : QObject(parent), m_worker(new FileReadWorker()), m_chunkSize(1000), m_isReading(false)
{
    m_worker->moveToThread(&m_workerThread);
    connect(&m_workerThread, &QThread::finished, m_worker, &QObject::deleteLater);
    connect(m_worker, &FileReadWorker::chunkReady, this, &AsyncFileReader::dataChunkReady);
    connect(m_worker, &FileReadWorker::progress, this, &AsyncFileReader::progressUpdated);
    connect(m_worker, &FileReadWorker::finished, this, [this](int totalEntries, qint64 elapsedMs) {
        m_isReading = false;
        emit readingCompleted(totalEntries, elapsedMs);
    });
    connect(m_worker, &FileReadWorker::error, this, [this](const QString& error) {
        m_isReading = false;
        emit errorOccurred(error);
    });
    m_workerThread.start();
}

AsyncFileReader::~AsyncFileReader() {
    m_workerThread.quit();
    m_workerThread.wait();
}

void AsyncFileReader::startReading(const QString& filePath, const QString& encoding, int maxEntries) {
    m_isReading = true;
    int estimatedLines = estimateLineCount(filePath);
    emit readingStarted(filePath, estimatedLines);
    QMetaObject::invokeMethod(m_worker, "doRead", Qt::QueuedConnection,
                              Q_ARG(QString, filePath),
                              Q_ARG(QString, encoding),
                              Q_ARG(int, m_chunkSize),
                              Q_ARG(int, maxEntries));
}

void AsyncFileReader::pauseReading() {
    QMetaObject::invokeMethod(m_worker, "pause", Qt::QueuedConnection);
}

void AsyncFileReader::resumeReading() {
    QMetaObject::invokeMethod(m_worker, "resume", Qt::QueuedConnection);
}

void AsyncFileReader::cancelReading() {
    m_isReading = false;
    QMetaObject::invokeMethod(m_worker, "cancel", Qt::QueuedConnection);
}

bool AsyncFileReader::isReading() const {
    return m_isReading;
}

void AsyncFileReader::setChunkSize(int chunkSize) {
    m_chunkSize = chunkSize;
}

int AsyncFileReader::estimateLineCount(const QString& filePath) const {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return 0;
    }

    // 读取文件的一小部分来估算行数
    const qint64 sampleSize = qMin(file.size(), qint64(8192));
    QByteArray sample = file.read(sampleSize);
    file.close();

    if (sample.isEmpty()) {
        return 0;
    }

    // 计算样本中的换行符数量
    int lineCount = sample.count('\n');
    if (lineCount == 0) {
        return 1; // 至少有一行
    }

    // 根据样本估算总行数
    double ratio = double(file.size()) / double(sampleSize);
    return int(lineCount * ratio);
}

